'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'

interface Bill {
  id: string
  name: string
  amount: number
  dueDate: string
  category: string
  isPaid: boolean
  isOverdue: boolean
}

export function UpcomingBills() {
  // Mock data - replace with actual data fetching
  const bills: Bill[] = [
    {
      id: '1',
      name: 'Rent',
      amount: 1200.00,
      dueDate: '2024-01-31',
      category: 'Housing',
      isPaid: false,
      isOverdue: false
    },
    {
      id: '2',
      name: 'Electric Bill',
      amount: 89.25,
      dueDate: '2024-01-28',
      category: 'Utilities',
      isPaid: false,
      isOverdue: false
    },
    {
      id: '3',
      name: 'Internet',
      amount: 65.00,
      dueDate: '2024-01-25',
      category: 'Utilities',
      isPaid: true,
      isOverdue: false
    },
    {
      id: '4',
      name: 'Phone Bill',
      amount: 45.00,
      dueDate: '2024-01-20',
      category: 'Utilities',
      isPaid: false,
      isOverdue: true
    },
    {
      id: '5',
      name: 'Car Insurance',
      amount: 125.50,
      dueDate: '2024-02-01',
      category: 'Insurance',
      isPaid: false,
      isOverdue: false
    }
  ]

  const totalUpcoming = bills
    .filter(bill => !bill.isPaid)
    .reduce((sum, bill) => sum + bill.amount, 0)

  const getDaysUntilDue = (dueDate: string) => {
    const today = new Date()
    const due = new Date(dueDate)
    const diffTime = due.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getBillStatus = (bill: Bill) => {
    if (bill.isPaid) return { text: 'Paid', color: 'text-green-600 bg-green-50' }
    if (bill.isOverdue) return { text: 'Overdue', color: 'text-red-600 bg-red-50' }
    
    const daysUntil = getDaysUntilDue(bill.dueDate)
    if (daysUntil <= 3) return { text: 'Due Soon', color: 'text-yellow-600 bg-yellow-50' }
    return { text: 'Upcoming', color: 'text-blue-600 bg-blue-50' }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-900">
          Upcoming Bills
        </CardTitle>
        <p className="text-sm text-gray-600">
          Total upcoming: {formatCurrency(totalUpcoming)}
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {bills.map((bill) => {
            const status = getBillStatus(bill)
            const daysUntil = getDaysUntilDue(bill.dueDate)
            
            return (
              <div
                key={bill.id}
                className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h4 className="font-medium text-gray-900">{bill.name}</h4>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${status.color}`}>
                      {status.text}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-sm text-gray-500">{bill.category}</span>
                    <span className="text-sm text-gray-400">•</span>
                    <span className="text-sm text-gray-500">
                      Due {new Date(bill.dueDate).toLocaleDateString()}
                    </span>
                    {!bill.isPaid && !bill.isOverdue && (
                      <>
                        <span className="text-sm text-gray-400">•</span>
                        <span className="text-sm text-gray-500">
                          {daysUntil > 0 ? `${daysUntil} days` : 'Today'}
                        </span>
                      </>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <span className={`font-semibold ${bill.isPaid ? 'text-gray-500 line-through' : 'text-gray-900'}`}>
                    {formatCurrency(bill.amount)}
                  </span>
                </div>
              </div>
            )
          })}
        </div>
        <div className="mt-6 text-center">
          <button className="text-blue-600 hover:text-blue-800 font-medium text-sm">
            Manage Bills
          </button>
        </div>
      </CardContent>
    </Card>
  )
}
